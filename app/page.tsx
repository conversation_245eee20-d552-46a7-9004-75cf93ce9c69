"use client";

import type React from "react";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Clock,
  Trophy,
  Share2,
  Copy,
  Heart,
  Zap,
  User,
  Crown,
  X,
} from "lucide-react";
import { FlipCounter } from "@/components/flip-counter";
import AppHeader from "@/components/layout/header";
import Cats from "@/components/layout/cats";
import Dogs from "@/components/layout/dogs";

export interface ClickAnimation {
  id: number;
  x: number;
  y: number;
  team: "cats" | "dogs";
}

// Mock data
const initialScores = {
  cats: 2847392,
  dogs: 2651847,
};

const mockDonors = [
  {
    name: "CatLover2024",
    team: "cats",
    amount: 5,
    clicks: 5000,
    time: "2 min ago",
  },
  {
    name: "<PERSON>goF<PERSON>",
    team: "dogs",
    amount: 10,
    clicks: 10000,
    time: "5 min ago",
  },
  {
    name: "Anonymous",
    team: "cats",
    amount: 1,
    clicks: 1000,
    time: "8 min ago",
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON>ow<PERSON>",
    team: "dogs",
    amount: 3,
    clicks: 3000,
    time: "12 min ago",
  },
];

export default function CatsVsDogsGame() {
  const [scores, setScores] = useState(initialScores);
  const [timeLeft, setTimeLeft] = useState({
    days: 15,
    hours: 8,
    minutes: 42,
    seconds: 18,
  });
  const [userClicks, setUserClicks] = useState({ cats: 0, dogs: 0 });
  const [clickAnimations, setClickAnimations] = useState<ClickAnimation[]>([]);
  const [animationId, setAnimationId] = useState(0);
  const [showStats, setShowStats] = useState(false);

  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 };
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 };
        } else if (prev.hours > 0) {
          return { ...prev, hours: prev.hours - 1, minutes: 59, seconds: 59 };
        } else if (prev.days > 0) {
          return {
            ...prev,
            days: prev.days - 1,
            hours: 23,
            minutes: 59,
            seconds: 59,
          };
        }
        return prev;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleTeamClick = useCallback(
    (team: "cats" | "dogs", event: React.MouseEvent) => {
      const rect = event.currentTarget.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      // Add click animation
      const newAnimation: ClickAnimation = {
        id: animationId,
        x,
        y,
        team,
      };

      setClickAnimations((prev) => [...prev, newAnimation]);
      setAnimationId((prev) => prev + 1);

      // Remove animation after 1 second
      setTimeout(() => {
        setClickAnimations((prev) =>
          prev.filter((anim) => anim.id !== newAnimation.id)
        );
      }, 1000);

      // Update scores
      setScores((prev) => ({
        ...prev,
        [team]: prev[team] + 1,
      }));

      setUserClicks((prev) => ({
        ...prev,
        [team]: prev[team] + 1,
      }));
    },
    [animationId]
  );

  const handleBoost = (team: "cats" | "dogs") => {
    setScores((prev) => ({
      ...prev,
      [team]: prev[team] + 1000,
    }));
    // toast({
    //   title: `🚀 Boosted ${team === "cats" ? "🐱 Cats" : "🐶 Dogs"}!`,
    //   description: "+1,000 clicks added to your team!",
    // });
  };

  const handleShare = (platform: string) => {
    const url = window.location.href;
    const text =
      "Join the epic battle! Cats vs Dogs - The Internet War! Every click counts! 🐱⚔️🐶";

    if (platform === "twitter") {
      window.open(
        `https://twitter.com/intent/tweet?text=${encodeURIComponent(
          text
        )}&url=${encodeURIComponent(url)}`,
        "_blank"
      );
    } else if (platform === "reddit") {
      window.open(
        `https://reddit.com/submit?title=${encodeURIComponent(
          text
        )}&url=${encodeURIComponent(url)}`,
        "_blank"
      );
    } else if (platform === "copy") {
      navigator.clipboard.writeText(url);
      // toast({
      //   title: "Link copied!",
      //   description: "Share it with your friends to join the battle!",
      // });
    }
  };

  const totalClicks = scores.cats + scores.dogs;
  const catsPercentage = (scores.cats / totalClicks) * 100;
  const dogsPercentage = (scores.dogs / totalClicks) * 100;
  const totalUserClicks = userClicks.cats + userClicks.dogs;

  return (
    <div className="h-screen bg-gray-900 text-white flex flex-col overflow-hidden">
      {/* Header - Part of Layout Flow */}
      <AppHeader setShowStats={setShowStats} />
      {/* Main Content Area - Takes Remaining Space */}
      <div className="flex-1 flex flex-col md:flex-row">
        {/* Cats Side */}
        <Cats
          handleTeamClick={handleTeamClick}
          handleBoost={handleBoost}
          scores={scores}
          clickAnimations={clickAnimations}
          catsPercentage={catsPercentage}
        />

        {/* Divider */}
        <div className="relative w-full md:w-2 h-2 md:h-full bg-gradient-to-r md:bg-gradient-to-b from-orange-500 via-yellow-500 to-blue-500 flex items-center justify-center">
          <div className="relative w-16 h-16 min-w-16 bg-gray-800 rounded-full flex items-center justify-center border-4 border-yellow-500 shadow-2xl z-10">
            <span className="text-3xl">⚔️</span>
          </div>
        </div>

        {/* Dogs Side */}
        <Dogs
          handleTeamClick={handleTeamClick}
          handleBoost={handleBoost}
          dogsPercentage={dogsPercentage}
          scores={scores}
          clickAnimations={clickAnimations}
        />
      </div>
      {/* Footer - Part of Layout Flow */}
      <div className="bg-gray-800 border-t border-gray-700 flex-shrink-0">
        <div className="px-4 py-3">
          <div className="flex items-center justify-center">
            {/* War Ends Timer */}
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-red-400" />
              <div className="text-red-400 text-sm font-semibold">
                WAR ENDS:
              </div>
              <div className="flex gap-1 text-xs">
                <span className="bg-red-600 px-2 py-1 rounded font-mono">
                  {timeLeft.days}d
                </span>
                <span className="bg-red-600 px-2 py-1 rounded font-mono">
                  {timeLeft.hours}h
                </span>
                <span className="bg-red-600 px-2 py-1 rounded font-mono">
                  {timeLeft.minutes}m
                </span>
                <span className="bg-red-600 px-2 py-1 rounded font-mono">
                  {timeLeft.seconds}s
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Panel */}
      {showStats && (
        <div className="fixed inset-0 z-40 bg-gray-900/95 backdrop-blur-sm">
          <div className="h-full overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-bold text-white flex items-center gap-2">
                  <Trophy className="w-6 h-6 text-yellow-400" />
                  Battle Stats
                </h2>
                <Button
                  variant="ghost"
                  onClick={() => setShowStats(false)}
                  className="text-gray-300 hover:text-white hover:bg-gray-700/50 transition-all duration-200"
                >
                  <X className="w-5 h-5" />
                </Button>
              </div>

              {/* Overall Progress */}
              <Card className="mb-4 bg-gray-800 border-gray-700">
                <CardContent className="p-4">
                  <h3 className="text-lg font-bold text-white mb-3 text-center">
                    Battle Progress
                  </h3>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-orange-400">
                        🐱 Cats:{" "}
                        <FlipCounter
                          value={scores.cats}
                          className=" font-bold text-xl"
                        />
                      </span>
                      <span className="text-blue-400">
                        🐶 Dogs:{" "}
                        <FlipCounter
                          value={scores.dogs}
                          className=" font-bold text-xl"
                        />
                      </span>
                    </div>
                    <div className="h-3 bg-gray-700 rounded-full overflow-hidden">
                      <div className="h-full flex">
                        <div
                          className="bg-gradient-to-r from-orange-500 to-pink-500 transition-all duration-500"
                          style={{ width: `${catsPercentage}%` }}
                        />
                        <div
                          className="bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-500"
                          style={{ width: `${dogsPercentage}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Your Contribution */}
              {totalUserClicks > 0 && (
                <Card className="mb-4 bg-gray-800 border-gray-700">
                  <CardContent className="p-4">
                    <h3 className="text-lg font-bold text-white mb-3 flex items-center gap-2">
                      <User className="w-5 h-5 text-green-400" />
                      Your Contribution
                    </h3>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center p-3 bg-orange-500/10 rounded-lg border border-orange-500/20">
                        <div className="text-xl font-bold text-orange-400">
                          <FlipCounter value={userClicks.cats} />
                        </div>
                        <div className="text-sm text-orange-300">
                          Cats Clicks
                        </div>
                      </div>
                      <div className="text-center p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                        <div className="text-xl font-bold text-blue-400">
                          <FlipCounter value={userClicks.dogs} />
                        </div>
                        <div className="text-sm text-blue-300">Dogs Clicks</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Boost Section */}
              <Card className="mb-4 bg-gradient-to-r from-yellow-900/20 to-orange-900/20 border-yellow-500/30">
                <CardContent className="p-4">
                  <div className="flex items-center justify-center gap-2 mb-3">
                    <Zap className="w-5 h-5 text-yellow-400" />
                    <h3 className="text-lg font-bold text-white">
                      Power Boost
                    </h3>
                    <Crown className="w-4 h-4 text-yellow-400" />
                  </div>
                  <p className="text-gray-300 text-center mb-3 text-sm">
                    $1 = 1,000 clicks for your team!
                  </p>
                  <div className="flex gap-2 justify-center">
                    <Button
                      onClick={() => handleBoost("cats")}
                      className="bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-600 hover:to-pink-600 shadow-lg transform hover:scale-105 transition-all duration-200"
                      size="sm"
                    >
                      ⚡ Boost Cats
                    </Button>
                    <Button
                      onClick={() => handleBoost("dogs")}
                      className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 shadow-lg transform hover:scale-105 transition-all duration-200"
                      size="sm"
                    >
                      ⚡ Boost Dogs
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Recent Boosters */}
              <Card className="mb-4 bg-gray-800 border-gray-700">
                <CardContent className="p-4">
                  <h3 className="text-lg font-bold text-white mb-3 flex items-center gap-2">
                    <Trophy className="w-5 h-5 text-yellow-400" />
                    Recent Boosters
                  </h3>
                  <div className="space-y-2">
                    {mockDonors.map((donor, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 bg-gray-700 rounded-lg"
                      >
                        <div className="flex items-center gap-2">
                          <div className="text-lg">
                            {donor.team === "cats" ? "🐱" : "🐶"}
                          </div>
                          <div>
                            <div className="text-sm font-semibold text-white">
                              {donor.name}
                            </div>
                            <div className="text-xs text-gray-400">
                              {donor.time}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-bold text-green-400">
                            ${donor.amount}
                          </div>
                          <div className="text-xs text-gray-400">
                            +{donor.clicks.toLocaleString()}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Share Section */}
              <Card className="mb-4 bg-gray-800 border-gray-700">
                <CardContent className="p-4 text-center">
                  <h3 className="text-lg font-bold text-white mb-3 flex items-center justify-center gap-2">
                    <Share2 className="w-5 h-5 text-blue-400" />
                    Recruit Warriors
                  </h3>
                  <div className="flex gap-2 justify-center flex-wrap">
                    <Button
                      onClick={() => handleShare("twitter")}
                      className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg transform hover:scale-105 transition-all duration-200"
                      size="sm"
                    >
                      <span className="mr-1">🐦</span>
                      X/Twitter
                    </Button>
                    <Button
                      onClick={() => handleShare("reddit")}
                      className="bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 shadow-lg transform hover:scale-105 transition-all duration-200"
                      size="sm"
                    >
                      <span className="mr-1">📱</span>
                      Reddit
                    </Button>
                    <Button
                      onClick={() => handleShare("copy")}
                      variant="outline"
                      className="border-gray-500 text-gray-300 hover:bg-gray-600 hover:text-white shadow-lg transform hover:scale-105 transition-all duration-200"
                      size="sm"
                    >
                      <Copy className="w-4 h-4 mr-1" />
                      Copy Link
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Footer */}
              <div className="text-center py-2">
                <Button
                  variant="outline"
                  className="border-gray-500 text-gray-300 hover:bg-gray-600 bg-transparent hover:text-white transform hover:scale-105 transition-all duration-200"
                  size="sm"
                  onClick={() =>
                    window.open("https://buymeacoffee.com/developer", "_blank")
                  }
                >
                  <Heart className="w-4 h-4 mr-2 text-red-400" />
                  Support Developer
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        @keyframes float-up {
          0% {
            opacity: 1;
            transform: translateY(0px) scale(1);
          }
          100% {
            opacity: 0;
            transform: translateY(-60px) scale(1.3);
          }
        }
      `}</style>
    </div>
  );
}
