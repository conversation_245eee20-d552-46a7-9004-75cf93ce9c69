import type { Metada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON>_<PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google";

const geistMono = Geist_Mono({
  subsets: ["latin"],
  variable: "--font-geist-mono",
});
const geist = Geist({ subsets: ["latin"], variable: "--font-geist-sans" });

import "./globals.css";
import { cn } from "@/lib/utils";

export const metadata: Metadata = {
  title: "Cats vs Dogs – The Internet Click War",
  description:
    "Join the ultimate Cats vs Dogs war! Click for your team, donate to boost their score, and watch real-time results. Every click counts. Ends August 31st.",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1.0,
  maximumScale: 1.0,
  userScalable: false,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link
          rel="apple-touch-icon"
          sizes="180x180"
          href="/apple-touch-icon.png"
        />
        <link
          rel="icon"
          type="image/png"
          sizes="32x32"
          href="/favicon-32x32.png"
        />
        <link
          rel="icon"
          type="image/png"
          sizes="16x16"
          href="/favicon-16x16.png"
        />
        <link rel="manifest" href="/site.webmanifest" />
      </head>
      <body
        className={cn(
          "touch-manipulation select-none",
          geistMono.variable,
          geist.variable
        )}
      >
        {children}
      </body>
    </html>
  );
}
