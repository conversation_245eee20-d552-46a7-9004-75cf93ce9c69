"use client";

import SlotCounter from "react-slot-counter";

interface FlipCounterProps {
  value: number;
  className?: string;
  duration?: number;
}

export function FlipCounter({
  value,
  className = "",
  duration = 0.2,
}: FlipCounterProps) {
  const displayStr = value.toLocaleString();
  const paddedDisplay = displayStr.padStart(displayStr.length, " ");

  return (
    <div className={`font-mono ${className}`}>
      <SlotCounter
        charClassName="font-mono"
        value={paddedDisplay}
        sequentialAnimationMode
        duration={duration}
      />
    </div>
  );
}
