import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Menu, User } from "lucide-react";
import Image from "next/image";

const AppHeader = ({
  setShowStats,
}: {
  setShowStats: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  return (
    <div className="bg-gray-800 border-b border-gray-700 flex-shrink-0">
      <div className="px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo and Title */}
          <div className="flex items-center gap-3">
            <div className="rounded-lg flex items-center justify-center shadow-lg">
              <Image src="/icon.png" alt="cats" width={50} height={50} />
            </div>
            <div>
              <div className="text-lg font-bold text-white">BATTLE ARENA</div>
              <div className="text-xs text-gray-400">Cats vs Dogs War</div>
            </div>
          </div>

          {/* Profile and Menu */}
          <div className="flex items-center gap-2">
            {true ? (
              <div className="flex items-center gap-2">
                <Avatar className="w-8 h-8">
                  <AvatarImage src="/placeholder.svg?height=32&width=32" />
                  <AvatarFallback className="bg-gradient-to-br from-purple-500 to-pink-500 text-white text-xs">
                    U
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="text-sm font-semibold text-white">Player</div>
                </div>
              </div>
            ) : (
              <Button
                variant="outline"
                size="sm"
                className="border-purple-500/50 text-purple-300 hover:bg-purple-500/10 hover:border-purple-400 hover:text-purple-200 transition-all duration-200 bg-purple-500/5"
              >
                <User className="w-4 h-4 mr-1" />
                Login
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowStats((prev) => !prev)}
              className="text-gray-300 hover:text-white hover:bg-gray-700/50 transition-all duration-200"
              name="Open Stats Panel"
              aria-label="Open Stats Panel"
            >
              <Menu className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppHeader;
